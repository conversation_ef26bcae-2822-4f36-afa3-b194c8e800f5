 StcRev Data |1.25|CT17-EN|OpENerPC|1623922735| DevData |255|0|500|500|500,500|X||ENet|
[<00>]-------|OpENer PC||
  General    |3.27|EtherNet/IP  Vol 2, Ed 1.25||2.3|Rockwell Automation/Allen-Bradley|1|65001|
  DevProfile |12|Communications Adapter|,04,05,06,07,08,09,10,11,15,16,29,30,32,33,43,55,243,244,245,246,35,67,71,72,77,84,85,86,87,69,81,68,70,82,78,79,80,83,92,256,257,258,259,260,261,262|
  PhysConf   ||X| |X| | | | | |0|X| | |
  LEDs       | | | | | |
  IP MAC Set | |X| |**************| | ||08:00:27:77:CF:56| | | |
  CRate Set  |X| | |X| | ||
  CRate Sup  |X|X| |X|X|
  Net Behavi | | ||| | | | | | | |
  Cxn Behavi | | | |X| | | | | | ||| | |-1|1000|1000| |511| |
  FO Path    ||||||||||||| |
  FO-IO Data | | | | | | | | | | | || | | | | | || | | |
  Cfg-IO Dat ||||
  Reserved   ||
  Safety Data| | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | | |
  Reserved   | |
  Safety IO  |||||||||||||||||||||||||||||||||||
  Safety Cfg |||||||||||| | | | |||2|
  Reserved   | |
[<01>]--------------------------------------------------
  CAG        |X|X|X| | |X|X| | |
  CAS        | | | | | | | | | |
  CAL        | | | | | | | | | |
  CS         |X| |X| | | | | | | | | |
  CSP        |||||||||||||
  IAG        |X|X|X|X|X|X|X| | | | | | | | | | | | | | | | | |
  IAS        | | | | | | | | | | | | | | | | | | | | | | | | |
  IAL        | =(1)| =(12)| =(65001)| =(2.3)| | | =(OpENer PC)| | | | | | | | | | | | | | | | | |
  IS         |X|X|X| | | |X|X| | | | |
  ISP        ||0,1|||||||||||
  VSA        | |
  OPT        | | |
[<02>]--------------------------------------------------
  CAG        |X|X|X| | |X|X| | |
  CAS        | | | | | | | | | |
  CAL        | | | | | | | | | |
  CS         |X|X| | | | | | | | | |
  CSP        ||||||||||||
  IAG        | | | | |
  IAS        | | | | |
  IAL        | | | | |
  IS         | |X| | | | | | | | | | |
  ISP        |||||||||||||
  VSA        | |
  OPT        | | |
[<04a>]--------------------------------------------------
  CAG        |X|X|X| | |X|X| | |
  CAS        | | | | | | | | | |
  CAL        | | | | | | | | | |
  CS         | | |X| | | | | | | | |
  CSP        ||||||||||||
  IAG        | | |X|X| |
  IAS        | | |X| | |
  IAL        | | | | | |
  IS         | |X|X| | | | | | | |
  ISP        |||||||||||
  VSA        | |
  OPT        | | |
  OPT        |100|
[<04b>]--------------------------------------------------
  CAG        |X|X|X| | |X|X| | |
  CAS        | | | | | | | | | |
  CAL        | | | | | | | | | |
  CS         | | |X| | | | | | | | |
  CSP        ||||||||||||
  IAG        | | |X|X| |
  IAS        | | |X| | |
  IAL        | | | | | |
  IS         | |X|X| | | | | | | |
  ISP        |||||||||||
  VSA        | |
  OPT        | | |
  OPT        |150,152,153|
[<04d>]--------------------------------------------------
  CAG        |X|X|X| | |X|X| | |
  CAS        | | | | | | | | | |
  CAL        | | | | | | | | | |
  CS         | | |X| | | | | | | | |
  CSP        ||||||||||||
  IAG        | | |X|X| |
  IAS        | | |X| | |
  IAL        | | | | | |
  IS         | |X|X| | | | | | | |
  ISP        |||||||||||
  VSA        | |
  OPT        | | |
  OPT        |151|
[<04c>]--------------------------------------------------
  CAG        |X|X|X| | |X|X| | |
  CAS        | | | | | | | | | |
  CAL        | | | | | | | | | |
  CS         | | |X| | | | | | | | |
  CSP        ||||||||||||
  IAG        | | |X|X| |
  IAS        | | |X| | |
  IAL        | | | | | |
  IS         | |X|X| | | | | | | |
  ISP        |||||||||||
  VSA        | |
  OPT        | | |
  OPT        |154|
[<06>]--------------------------------------------------
  CAG        |X|X|X| | |X|X| | |
  CAS        | | | | | | | | | |
  CAL        | | | | | | | | | |
  CS         |X|X| | | | | | | | |
  CSP        |||||||||||
  IAG        | | | | | | | | | | | | | | | | | | | |
  IAS        | | | | | | | | | | | | | | | | | | | |
  IAL        | | | | | | | | | | | | | | | | | | | |
  IS         | | |X| |X| |X|X|X|X|X| | | | | | | | |
  ISP        ||||||||||||||||||||
  VSA        | |
  OPT        | | |
  OPT        |<PossibleConnections><CIPConnection connectionNameString="demo exclusive owner connection point" ePath="200424972c962c64"><TriggerAndTransport class1="1" server="1" triggerCyclic="1"><TransportTypeExclusiveOwner transportTypeExclusiveOwner="1"/></TriggerAndTransport><Originator2TargetParameters><ConnectionParameters fixedSizeSupported="1" realTimeTransferFormat="4" connectionTypePoint2Point="1" priorityLow="1"/><Size><Constant constValue="32" minValue="32" maxValue="32"/></Size></Originator2TargetParameters><Target2OriginatorParameters><ConnectionParameters fixedSizeSupported="1" realTimeTransferFormat="0" connectionTypeMulticast="1" connectionTypePoint2Point="1" priorityLow="1"/><Size><Constant constValue="32" minValue="32" maxValue="32"/></Size></Target2OriginatorParameters></CIPConnection><CIPConnection connectionNameString="demo input only" ePath="200424972c982c64"><TriggerAndTransport class1="1" server="1" triggerCyclic="1"><TransportTypeInputOnly transportTypeInputOnly="1"/></TriggerAndTransport><Originator2TargetParameters><ConnectionParameters fixedSizeSupported="1" realTimeTransferFormat="3" connectionTypePoint2Point="1" priorityLow="1"/><Size><Constant constValue="0" minValue="0" maxValue="0"/></Size></Originator2TargetParameters><Target2OriginatorParameters><ConnectionParameters variableSizeSupported="1" realTimeTransferFormat="1" connectionTypeMulticast="1" connectionTypePoint2Point="1" priorityLow="1"/><Size><Constant constValue="32" minValue="32" maxValue="32"/></Size></Target2OriginatorParameters></CIPConnection><CIPConnection connectionNameString="demo listen only" ePath="200424972c992c64"><TriggerAndTransport class1="1" server="1" triggerCyclic="1"><TransportTypeListenOnly transportTypeListenOnly="1"/></TriggerAndTransport><Originator2TargetParameters><ConnectionParameters fixedSizeSupported="1" realTimeTransferFormat="3" connectionTypePoint2Point="1" priorityLow="1"/><Size><Constant constValue="0" minValue="0" maxValue="0"/></Size></Originator2TargetParameters><Target2OriginatorParameters><ConnectionParameters variableSizeSupported="1" realTimeTransferFormat="1" connectionTypeMulticast="1" connectionTypePoint2Point="1" priorityLow="1"/><Size><Constant constValue="32" minValue="32" maxValue="32"/></Size></Target2OriginatorParameters></CIPConnection></PossibleConnections>|
[<245>]--------------------------------------------------
  CAG        |X|X|X| | |X|X| | |
  CAS        | | | | | | | | | |
  CAL        | | | | | | | | | |
  CS         |X|X| | | | | | | | |
  CSP        |||||||||||
  IAG        |X|X|X|X|X|X|X|X| | | |X| | | | |
  IAS        | | |X| | | | | | | | | | | | | |
  IAL        | =(1,17)| =(4)| | | | | =(1..255)| | | | | =(0..3600)| | | | |
  IS         |X| |X|X| | | | | | | | | | |
  ISP        |||||||||||||||
  VSA        | |
  OPT        | | |
[<246>]--------------------------------------------------
  CAG        |X|X|X| | |X|X| | |
  CAS        | | | | | | | | | |
  CAL        | | | | | | | | | |
  CS         |X|X| | | | | | | | |
  CSP        |||||||||||
  IAG        |X|X|X| | | |X| | | |X| | | | |
  IAS        | | | | | | | | | | | | | | | |
  IAL        | =(0,10,100)| |MAC address (08:00:27:77:CF:56)| | | | | | | | | | | | |
  IS         |X|X| | | | | | | | | | |
  ISP        |||||||||||||
  VSA        | |
  OPT        | | |
[<72>]--------------------------------------------------
  CAG        |X|X|X| | |X|X| | |
  CAS        | | | | | | | | | |
  CAL        | | | | | | | | | |
  CS         |X|X| | | | | | | | |
  CSP        |||||||||||
  IAG        | | | |X|X|X|X|X|
  IAS        | | | |X|X|X|X|X|
  IAL        | =(0)| =(59)| =(47)| =(55)| =(47)| =(43)| =(31)| =(27)|
  IS         |X|X| | | | | | | |
  ISP        ||||||||||
  VSA        | |
  OPT        | | |
[--------------------------------------------------------
