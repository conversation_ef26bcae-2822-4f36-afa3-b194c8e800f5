$ EZ-EDS Version 3.23.1.20171205 Generated Electronic Data Sheet

[File]
        DescText = "EDS file for the sample application of OpENer";
        CreateDate = 11-03-2009;
        CreateTime = 13:15:23;
        ModDate = 02-06-2018;
        ModTime = 14:05:38;
        Revision = 2.3;
        HomeURL = "https://github.com/EIPStackGroup/OpENer";

[Device]
        VendCode = 1;
        VendName = "Rockwell Automation";
        ProdType = 12;
        ProdTypeStr = "Communications Adapter";
        ProdCode = 65001;
        MajRev = 2;
        MinRev = 3;
        ProdName = "OpENer PC";
        Catalog = "OpENer-2.x";

[Device Classification]
        Class1 = EtherNetIP;

[Params]
        Param1 =
                0,                      $ reserved, shall equal 0
                ,,                      $ Link Path Size, Link Path
                0x0000,                 $ Descriptor
                0xD1,                   $ Data Type
                1,                      $ Data Size in bytes
                "Input Data",           $ name
                "",                     $ units
                "New Help String",      $ help string
                ,,0,                    $ min, max, default data values
                ,,,,                    $ mult, div, base, offset scaling
                ,,,,                    $ mult, div, base, offset links
                ;                       $ decimal places
        Param2 =
                0,                      $ reserved, shall equal 0
                ,,                      $ Link Path Size, Link Path
                0x0000,                 $ Descriptor
                0xD1,                   $ Data Type
                1,                      $ Data Size in bytes
                "Output Data",          $ name
                "",                     $ units
                "New Help String",      $ help string
                ,,0,                    $ min, max, default data values
                ,,,,                    $ mult, div, base, offset scaling
                ,,,,                    $ mult, div, base, offset links
                ;                       $ decimal places
        Param3 =
                0,                      $ reserved, shall equal 0
                ,,                      $ Link Path Size, Link Path
                0x0000,                 $ Descriptor
                0xD1,                   $ Data Type
                1,                      $ Data Size in bytes
                "Config Data",          $ name
                "",                     $ units
                "New Help String",      $ help string
                ,,0,                    $ min, max, default data values
                ,,,,                    $ mult, div, base, offset scaling
                ,,,,                    $ mult, div, base, offset links
                ;                       $ decimal places
        Param4 =
                0,                      $ reserved, shall equal 0
                ,,                      $ Link Path Size, Link Path
                0x0000,                 $ Descriptor
                0xC8,                   $ Data Type
                4,                      $ Data Size in bytes
                "RPI",                  $ name
                "",                     $ units
                "New Help String",      $ help string
                20000,,30000,           $ min, max, default data values
                ,,,,                    $ mult, div, base, offset scaling
                ,,,,                    $ mult, div, base, offset links
                ;                       $ decimal places

[Assembly]
        Object_Name = "Assembly Object";
        Object_Class_Code = 0x04;
        Number_Of_Static_Instances = 6;
        Assem100 =
                "Input Assembly",
                "",
                32,
                0x0000,
                ,,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1,
                8,Param1;
        Assem150 =
                "Output Assembly",
                "",
                32,
                0x0001,
                ,,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2,
                8,Param2;
        Assem151 =
                "Config Assembly",
                "",
                10,
                0x0001,
                ,,
                8,Param3,
                8,Param3,
                8,Param3,
                8,Param3,
                8,Param3,
                8,Param3,
                8,Param3,
                8,Param3,
                8,Param3,
                8,Param3;

[Connection Manager]
        Revision = 1;
        Object_Name = "Connection Manager Object";
        Object_Class_Code = 0x06;
        MaxInst = 1;
        Number_Of_Static_Instances = 1;
        Max_Number_Of_Dynamic_Instances = 0;
        Connection1 =
                0x84010002,             $ 0-15    = supported transport classes
                                        $ 16      = trigger: cyclic
                                        $ 17      = trigger: change of state
                                        $ 18      = trigger: application
                                        $ 19-23   = trigger: reserved
                                        $ 24      = transport type: listen-only
                                        $ 25      = transport type: input-only
                                        $ 26      = transport type: exclusive-owner
                                        $ 27      = transport type: redundant-owner
                                        $ 28-30   = reserved
                                        $ 31      = Client = 0 / Server = 1
                0x44640405,             $ 0       = O->T fixed size supported
                                        $ 1       = O->T variable size supported
                                        $ 2       = T->O fixed size supported
                                        $ 3       = T->O variable size supported
                                        $ 4-5     = O->T number of bytes per slot (obsolete)
                                        $ 6-7     = T->O number of bytes per slot (obsolete)
                                        $ 8-10    = O->T Real time transfer format
                                        $ 11      = reserved
                                        $ 12-14   = T->O Real time transfer format
                                        $ 15      = reserved
                                        $ 16      = O->T connection type: NULL
                                        $ 17      = O->T connection type: MULTICAST
                                        $ 18      = O->T connection type: POINT2POINT
                                        $ 19      = O->T connection type: reserved
                                        $ 20      = T->O connection type: NULL
                                        $ 21      = T->O connection type: MULTICAST
                                        $ 22      = T->O connection type: POINT2POINT
                                        $ 23      = T->O connection type: reserved
                                        $ 24      = O->T priority: LOW
                                        $ 25      = O->T priority: HIGH
                                        $ 26      = O->T priority: SCHEDULED
                                        $ 27      = O->T priority: reserved
                                        $ 28      = T->O priority: LOW
                                        $ 29      = T->O priority: HIGH
                                        $ 30      = T->O priority: SCHEDULED
                                        $ 31      = T->O priority: reserved
                Param4,,Assem150,       $ O->T RPI, size, format
                Param4,,Assem100,       $ T->O RPI, size, format
                ,,                      $ config #1 size, format
                ,Assem151,              $ config #2 size, format
                "Exlusive Owner",       $ Connection Name
                "",                     $ help string
                "20 04 24 97 2C 96 2C 64";    $ Path
        Connection2 =
                0x02010002,             $ 0-15    = supported transport classes
                                        $ 16      = trigger: cyclic
                                        $ 17      = trigger: change of state
                                        $ 18      = trigger: application
                                        $ 19-23   = trigger: reserved
                                        $ 24      = transport type: listen-only
                                        $ 25      = transport type: input-only
                                        $ 26      = transport type: exclusive-owner
                                        $ 27      = transport type: redundant-owner
                                        $ 28-30   = reserved
                                        $ 31      = Client = 0 / Server = 1
                0x44640305,             $ 0       = O->T fixed size supported
                                        $ 1       = O->T variable size supported
                                        $ 2       = T->O fixed size supported
                                        $ 3       = T->O variable size supported
                                        $ 4-5     = O->T number of bytes per slot (obsolete)
                                        $ 6-7     = T->O number of bytes per slot (obsolete)
                                        $ 8-10    = O->T Real time transfer format
                                        $ 11      = reserved
                                        $ 12-14   = T->O Real time transfer format
                                        $ 15      = reserved
                                        $ 16      = O->T connection type: NULL
                                        $ 17      = O->T connection type: MULTICAST
                                        $ 18      = O->T connection type: POINT2POINT
                                        $ 19      = O->T connection type: reserved
                                        $ 20      = T->O connection type: NULL
                                        $ 21      = T->O connection type: MULTICAST
                                        $ 22      = T->O connection type: POINT2POINT
                                        $ 23      = T->O connection type: reserved
                                        $ 24      = O->T priority: LOW
                                        $ 25      = O->T priority: HIGH
                                        $ 26      = O->T priority: SCHEDULED
                                        $ 27      = O->T priority: reserved
                                        $ 28      = T->O priority: LOW
                                        $ 29      = T->O priority: HIGH
                                        $ 30      = T->O priority: SCHEDULED
                                        $ 31      = T->O priority: reserved
                Param4,0,,              $ O->T RPI, size, format
                Param4,32,Assem100,     $ T->O RPI, size, format
                ,,                      $ config #1 size, format
                0,,                     $ config #2 size, format
                "Input Only",           $ Connection Name
                "",                     $ help string
                "20 04 24 97 2C 98 2C 64";    $ Path
        Connection3 =
                0x01010002,             $ 0-15    = supported transport classes
                                        $ 16      = trigger: cyclic
                                        $ 17      = trigger: change of state
                                        $ 18      = trigger: application
                                        $ 19-23   = trigger: reserved
                                        $ 24      = transport type: listen-only
                                        $ 25      = transport type: input-only
                                        $ 26      = transport type: exclusive-owner
                                        $ 27      = transport type: redundant-owner
                                        $ 28-30   = reserved
                                        $ 31      = Client = 0 / Server = 1
                0x44240305,             $ 0       = O->T fixed size supported
                                        $ 1       = O->T variable size supported
                                        $ 2       = T->O fixed size supported
                                        $ 3       = T->O variable size supported
                                        $ 4-5     = O->T number of bytes per slot (obsolete)
                                        $ 6-7     = T->O number of bytes per slot (obsolete)
                                        $ 8-10    = O->T Real time transfer format
                                        $ 11      = reserved
                                        $ 12-14   = T->O Real time transfer format
                                        $ 15      = reserved
                                        $ 16      = O->T connection type: NULL
                                        $ 17      = O->T connection type: MULTICAST
                                        $ 18      = O->T connection type: POINT2POINT
                                        $ 19      = O->T connection type: reserved
                                        $ 20      = T->O connection type: NULL
                                        $ 21      = T->O connection type: MULTICAST
                                        $ 22      = T->O connection type: POINT2POINT
                                        $ 23      = T->O connection type: reserved
                                        $ 24      = O->T priority: LOW
                                        $ 25      = O->T priority: HIGH
                                        $ 26      = O->T priority: SCHEDULED
                                        $ 27      = O->T priority: reserved
                                        $ 28      = T->O priority: LOW
                                        $ 29      = T->O priority: HIGH
                                        $ 30      = T->O priority: SCHEDULED
                                        $ 31      = T->O priority: reserved
                Param4,0,,              $ O->T RPI, size, format
                Param4,,Assem100,       $ T->O RPI, size, format
                ,,                      $ config #1 size, format
                ,,                      $ config #2 size, format
                "Listen Only",          $ Connection Name
                "",                     $ help string
                "20 04 24 97 2C 99 2C 64";    $ Path

[Capacity]
        MaxMsgConnections = 6;
        MaxIOProduceConsume = 2;
        MaxIOMcastProducers = 1;
        MaxIOMcastConsumers = 1;
        MaxConsumersPerMcast = 6;
        TSpec1 = TxRx, 32, 100;

[TCP/IP Interface Class]
        Revision = 4;
        Object_Name = "TCP/IP Interface Object";
        Object_Class_Code = 0xF5;
        MaxInst = 1;
        Number_Of_Static_Instances = 1;
        Max_Number_Of_Dynamic_Instances = 0;

[Ethernet Link Class]
        Revision = 4;
        Object_Name = "Ethernet Link Object";
        Object_Class_Code = 0xF6;
        MaxInst = 1;
        Number_Of_Static_Instances = 1;
        Max_Number_Of_Dynamic_Instances = 0;

[Identity Class]
        Revision = 1;
        Object_Name = "Identity Object";
        Object_Class_Code = 0x01;
        MaxInst = 1;
        Number_Of_Static_Instances = 1;
        Max_Number_Of_Dynamic_Instances = 0;

[QoS Class]
        Revision = 1;
        Object_Name = "QoS Object";
        Object_Class_Code = 0x48;
        MaxInst = 1;
        Number_Of_Static_Instances = 1;
        Max_Number_Of_Dynamic_Instances = 0;

