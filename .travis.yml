language: c
branches:
  except:
  - gh-pages
env:
  global:
  - GH_REPO_NAME: OpENer
  - DOXYFILE: opener.doxyfile
  - GH_REPO_REF: github.com/EIPStackGroup/OpENer.git
  - secure: h1vuX5cGZd5W7f5TitD+EamJIsvG2qq8aBpO9MUGIOj3bShwTaR0S0qbcpCyltXiZ9DJklLc7kP5kB0XtX1o6vZMelQsqBjiHQK5yFW0vHmFAg1sLMpVBbsAN0lMWgeGJEmyRstA1KYBixwExtc5GpcgMBvS/mnJQ10zboZNcRU=
  - secure: CN1E5LJZwa7PLJOcst1MNb1c5Nx1rM9ifrc4llevRyTRyHxJ2S15mVQdnCvMcdPFK+ranBdsl1WcIxP3BQtI4zDwj0UWjj44EHz67VTp1o3zuaCa0fExYUwbe0D8uGRP4XbX+B4+HQWneGbabOLAZcS3Gc/pUpC3WEJO2pO2BHg=
addons:
  apt:
    packages:
    - libcap-dev
    - lcov
    - doxygen
    - doxygen-doc
    - doxygen-latex
    - doxygen-gui
    - graphviz
  sonarcloud:
    organization: eipstackgroup
    token: $SONAR_TOKEN

jobs:
  - name: "GCC on Linux"
    os: linux
    dist: xenial
    env: CMAKE_URL="https://cmake.org/files/v3.7/cmake-3.7.2-Linux-x86_64.tar.gz"

    install:
      - git fetch --unshallow --tags
      - DEPS_DIR="${TRAVIS_BUILD_DIR}/deps"
      - mkdir -p ${DEPS_DIR} && cd ${DEPS_DIR}
      - |
        mkdir cmake && travis_retry wget --no-check-certificate --quiet -O - ${CMAKE_URL} | tar --strip-components=1 -xz -C cmake
        export PATH=${DEPS_DIR}/cmake/bin:${PATH}
        cmake --version
    before_script:
      - $TRAVIS_BUILD_DIR/travis_scripts/linuxBeforeScript.sh
    script:
      - $TRAVIS_BUILD_DIR/travis_scripts/linuxScript.sh
    after_success:
      - $TRAVIS_BUILD_DIR/travis_scripts/linuxAfterSuccessScript.sh
  - name: "MSVC on Windows"
    os: windows
    script:
      - $TRAVIS_BUILD_DIR/travis_scripts/windowsScript.sh
  - name: "MinGW on Windows"
    os: windows
    env:
      - CC="/c/ProgramData/chocolatey/lib/mingw/tools/install/mingw64/bin/x86_64-w64-mingw32-gcc"
      - CXX="/c/ProgramData/chocolatey/lib/mingw/tools/install/mingw64/bin/x86_64-w64-mingw32-g++"
    script:
      - $TRAVIS_BUILD_DIR/travis_scripts/windowsMinGWScript.sh