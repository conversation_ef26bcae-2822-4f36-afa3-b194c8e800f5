sonar.projectKey=EIPStackGroup_OpENer
sonar.organization=eipstackgroup
sonar.python.version=3.8
sonar.language=c
sonar.c.file.suffixes=.c,.h
sonar.cpp.file.suffixes=.cc,.cpp,.cxx,.c++,.hh,.hpp,.hxx,.h++,.ipp
sonar.coverage.exclusions=fuzz/**, source/tests/**

# This is the name and version displayed in the SonarCloud UI.
#sonar.projectName=OpENer
#sonar.projectVersion=1.0

# Path is relative to the sonar-project.properties file. Replace "\" by "/" on Windows.
#sonar.sources=.

# Encoding of the source code. Default is default system encoding
#sonar.sourceEncoding=UTF-8