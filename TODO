This file serves as brainstorming buffer for ideas to improve and enhance OpENer:

* New Features:
  - Implementation of common CIP-objects
  - CIP-Sync
  - CIP-Motion


* Improvements and Optimizations
  - Remove the need for the response buffer in the explicit message handling 
  	(zero copy stack)
  - Rework I/O message handling:
     - own buffers for each connection that are preconfigured and only runtime 
       data needs to be changed.
  - Rework socket handling 
     - Use only one single UDP socket for all I/O messages
         - may not be possible for point to point consuming connections where
           the scanner gives a different port number
     - use the registered port number 2222 for all I/O communication 
     
