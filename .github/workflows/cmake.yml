name: CMake

on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]

env:
  # Customize the CMake build type here (Release, Debug, RelWithDebInfo, etc.)
  BUILD_TYPE: Release
  BUILD_WRAPPER_OUT_DIR: build_wrapper_output_directory # Directory where build-wrapper output will be placed

jobs:
  build:
    # The CMake configure and build commands are platform agnostic and should work equally well on Windows or Mac.
    # You can convert this to a matrix build if you need cross-platform coverage.
    # See: https://docs.github.com/en/free-pro-team@latest/actions/learn-github-actions/managing-complex-workflows#using-a-build-matrix
    name: Build
    runs-on: ubuntu-latest
    env:
      BUILD_WRAPPER_OUT_DIR: build_wrapper_output_directory # Directory where build-wrapper output will be placed

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Shallow clones should be disabled for a better relevancy of analysis

      - name: Set up Python 3.8 for gcovr
        uses: actions/setup-python@v5
        with: 
          python-version: 3.8

      - name: install gcovr 5.0
        run: |
          pip install gcovr==5.0 # 5.1 is not supported
          
      - name: Install libcap, lcov, and CppUTest
        run: sudo apt-get install -y libcap-dev lcov cpputest

      - name: Install sonar-scanner and build-wrapper
        uses: SonarSource/sonarcloud-github-c-cpp@v3

      - name: Configure CMake
        # Configure CMake in a 'build' subdirectory. `CMAKE_BUILD_TYPE` is only required if you are using a single-configuration generator such as make.
      #  See https://cmake.org/cmake/help/latest/variable/CMAKE_BUILD_TYPE.html?highlight=cmake_build_type
        run: cmake -S ${{github.workspace}}/source -B ${{github.workspace}}/build -DCMAKE_BUILD_TYPE=${{env.BUILD_TYPE}} -DOpENer_PLATFORM:STRING="POSIX" -DBUILD_SHARED_LIBS:BOOL=OFF -DOpENer_TRACES:BOOL=OFF -DOpENer_TESTS:BOOL=ON -DCPPUTEST_HOME:PATH=/usr

      - name: Build
        # Build your program with the given configuration
        run: |
          build-wrapper-linux-x86-64 --out-dir ${{ env.BUILD_WRAPPER_OUT_DIR }} cmake --build ${{github.workspace}}/build --config ${{env.BUILD_TYPE}}

      - name: Test
        working-directory: ${{github.workspace}}/build
        # Execute tests defined by the CMake configuration.
        # See https://cmake.org/cmake/help/latest/manual/ctest.1.html for more detail
        run: ctest -C ${{env.BUILD_TYPE}}

      - name: Collect coverage into one XML report
        run: |
          gcovr --sonarqube > coverage.xml
    
      - name: Run sonar-scanner
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN  }} # Put the name of your token here
        run: |
          sonar-scanner \
            --define sonar.cfamily.build-wrapper-output="${{ env.BUILD_WRAPPER_OUT_DIR }}" \
            --define sonar.coverageReportPaths=coverage.xml
