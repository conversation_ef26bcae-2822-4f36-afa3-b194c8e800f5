<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputType>Library</OutputType>
    <AssemblyName>FXEIPDelegation</AssemblyName>
    <PlatformTarget>arm64</PlatformTarget>
    <PublishAot>true</PublishAot>
    <NativeLib>Shared</NativeLib>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <!-- Optional optimizations -->
    <IlcOptimizationPreference>Size</IlcOptimizationPreference>
  </PropertyGroup>

  <PropertyGroup Condition="'$(RuntimeIdentifier)' == 'linux-arm64'">
    <CppCompilerAndLinker>/root/FXOpENer/source/src/csharp/gcc-wrapper.sh</CppCompilerAndLinker>
    <SysRoot>/usr/aarch64-linux-gnu</SysRoot>
    <LinkerFlavor>gcc</LinkerFlavor>
    <!-- Disable debug symbol extraction to avoid objcopy cross-compilation issues -->
    <StripSymbols>false</StripSymbols>
    <IlcGenerateDebugInformation>false</IlcGenerateDebugInformation>
  </PropertyGroup>

</Project>
