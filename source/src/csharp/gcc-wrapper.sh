#!/bin/bash

# GCC wrapper script to filter out unsupported parameters for ARM64 cross-compilation

# Filter out problematic parameters that .NET passes but GCC doesn't understand
filtered_args=()
skip_next=false

for arg in "$@"; do
    if [ "$skip_next" = true ]; then
        skip_next=false
        continue
    fi

    # Skip --target parameter (GCC doesn't need it for cross-compilation)
    if [[ "$arg" == "--target=aarch64-linux-gnu" ]] || [[ "$arg" == "--target=arm64" ]]; then
        continue
    elif [[ "$arg" == "--target" ]]; then
        skip_next=true
        continue
    # Skip -gz=zlib if not supported
    elif [[ "$arg" == "-gz=zlib" ]]; then
        # Check if GCC supports this flag, if not skip it
        if ! aarch64-linux-gnu-gcc --help=optimizers 2>/dev/null | grep -q "gz="; then
            continue
        fi
        filtered_args+=("$arg")
    # Convert -fuse-ld=gcc to -fuse-ld=bfd for ARM64 GCC
    elif [[ "$arg" == "-fuse-ld=gcc" ]]; then
        filtered_args+=("-fuse-ld=bfd")
    else
        filtered_args+=("$arg")
    fi
done

# Add ARM64 library paths and call the real cross-compiler with filtered arguments
exec aarch64-linux-gnu-gcc -L/usr/lib/aarch64-linux-gnu "${filtered_args[@]}"
