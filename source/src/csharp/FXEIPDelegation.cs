using System;
using System.Runtime.InteropServices;
using System.Runtime.CompilerServices;

namespace FX.EIP.Delegation
{
    public static class EIPDelegates{

        [UnmanagedCallersOnly(EntryPoint = "PrintHelloWorld")]
        public static void PrintHelloWorld()
        {
            Console.WriteLine("Hello world");
        }

        [UnmanagedCallersOnly(EntryPoint = "handleAppOutputAssemblyData")]
        public static unsafe void HandleAppOutputAssemblyData(byte* sourceData, byte* destData, int sourceDataSize, int destDataSize)
        {
            // Handle output assembly data - copy from source (g_assembly_data096) to destination (g_assembly_data064)
            // Use the smaller of the two sizes to prevent buffer overflow
            int copySize = sourceDataSize < destDataSize ? sourceDataSize : destDataSize;

            for (int i = 0; i < copySize; i++)
            {
                destData[i] = sourceData[i];
            }

            Console.WriteLine($"Output assembly data handled: {copySize} bytes copied from g_assembly_data096 ({sourceDataSize} bytes) to g_assembly_data064 ({destDataSize} bytes)");
        }
    }
}
