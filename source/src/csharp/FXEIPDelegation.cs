using System;
using System.Runtime.InteropServices;
using System.Runtime.CompilerServices;

namespace FX.EIP.Delegation
{
    public static class EIPDelegates{

        [UnmanagedCallersOnly(EntryPoint = "PrintHelloWorld")]
        public static void PrintHelloWorld()
        {
            Console.WriteLine("Hello world");
        }

        [UnmanagedCallersOnly(EntryPoint = "CopyAssemblyData")]
        public static unsafe void CopyAssemblyData(byte* sourceData, byte* destData, int dataSize)
        {
            // Copy data from source (g_assembly_data096) to destination (g_assembly_data064)
            for (int i = 0; i < dataSize; i++)
            {
                destData[i] = sourceData[i];
            }

            Console.WriteLine($"Assembly data copied: {dataSize} bytes from output to input assembly");
        }
    }
}
