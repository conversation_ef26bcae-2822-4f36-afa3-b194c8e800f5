using System;
using System.Runtime.InteropServices;
using System.Runtime.CompilerServices;

namespace FX.EIP.Delegation
{
    public static class EIPDelegates{

        [UnmanagedCallersOnly(EntryPoint = "PrintHelloWorld")]
        public static void PrintHelloWorld()
        {
            Console.WriteLine("Hello world");
        }

        [UnmanagedCallersOnly(EntryPoint = "handleAppOutputAssemblyData")]
        public static unsafe void HandleAppOutputAssemblyData(byte* sourceData, byte* destData, int sourceDataSize)
        {
            // Handle output assembly data - copy from source (g_assembly_data096) to destination (g_assembly_data064)
            // sourceDataSize is the size of g_assembly_data096 array
            for (int i = 0; i < sourceDataSize; i++)
            {
                destData[i] = sourceData[i];
            }

            Console.WriteLine($"Output assembly data handled: {sourceDataSize} bytes processed from g_assembly_data096 to g_assembly_data064");
        }
    }
}
