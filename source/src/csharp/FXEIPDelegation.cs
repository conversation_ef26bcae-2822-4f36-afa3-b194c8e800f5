using System;
using System.Runtime.InteropServices;
using System.Runtime.CompilerServices;

namespace FX.EIP.Delegation
{
    public static class EIPDelegates{

        [UnmanagedCallersOnly(EntryPoint = "PrintHelloWorld")]
        public static void PrintHelloWorld()
        {
            Console.WriteLine("Hello world");
        }

        [UnmanagedCallersOnly(EntryPoint = "handleAppOutputAssemblyData")]
        public static unsafe void HandleAppOutputAssemblyData(byte* sourceData, byte* destData, int dataSize)
        {
            // Handle output assembly data - copy from source (g_assembly_data096) to destination (g_assembly_data064)
            for (int i = 0; i < dataSize; i++)
            {
                destData[i] = sourceData[i];
            }

            Console.WriteLine($"Output assembly data handled: {dataSize} bytes processed from output to input assembly");
        }
    }
}
