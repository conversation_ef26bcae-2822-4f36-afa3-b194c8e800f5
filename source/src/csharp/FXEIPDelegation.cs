using System;
using System.Runtime.InteropServices;
using System.Runtime.CompilerServices;

namespace FX.EIP.Delegation
{
    public static class EIPDelegates{

        [UnmanagedCallersOnly(EntryPoint = "PrintHelloWorld")]
        public static void PrintHelloWorld()
        {
            Console.WriteLine("Hello world");
        }

        [UnmanagedCallersOnly(EntryPoint = "handleAppOutputAssemblyData")]
        public static unsafe void HandleAppOutputAssemblyData(byte* sourceData, nuint sourceDataSize, byte* destData, nuint destDataSize)
        {
            // Handle output assembly data - copy from source (g_assembly_data096) to destination (g_assembly_data064)
            // Use the smaller of the two sizes to prevent buffer overflow
            nuint copySize = sourceDataSize < destDataSize ? sourceDataSize : destDataSize;

            for (nuint i = 0; i < copySize; i++)
            {
                destData[i] = sourceData[i];
            }

            Console.WriteLine($"Output assembly data handled: {copySize} bytes copied from g_assembly_data096 ({sourceDataSize} bytes) to g_assembly_data064 ({destDataSize} bytes)");
        }

        [UnmanagedCallersOnly(EntryPoint = "printAssemblyData")]
        public static unsafe void PrintAssemblyData(byte* sourceData, nuint sourceDataSize, byte* destData, nuint destDataSize)
        {
            Console.WriteLine("=== Assembly Data Contents ===");

            // Print g_assembly_data096 (source/output assembly)
            Console.Write($"g_assembly_data096 ({sourceDataSize} bytes): ");
            for (nuint i = 0; i < sourceDataSize; i++)
            {
                Console.Write($"{sourceData[i]:X2} ");
            }
            Console.WriteLine();

            // Print g_assembly_data064 (dest/input assembly)
            Console.Write($"g_assembly_data064 ({destDataSize} bytes): ");
            for (nuint i = 0; i < destDataSize; i++)
            {
                Console.Write($"{destData[i]:X2} ");
            }
            Console.WriteLine();
            Console.WriteLine("==============================");
        }
    }
}
