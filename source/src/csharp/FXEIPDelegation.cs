using System;
using System.Runtime.InteropServices;
using System.Runtime.CompilerServices;

namespace FX.EIP.Delegation
{
    public static class EIPDelegates{

        [UnmanagedCallersOnly(EntryPoint = "PrintHelloWorld")]
        public static void PrintHelloWorld()
        {
            Console.WriteLine("Hello world");
        }

        [UnmanagedCallersOnly(EntryPoint = "handleAppOutputAssemblyData")]
        public static unsafe void HandleAppOutputAssemblyData(byte* sourceData, nuint sourceDataSize, byte* destData, nuint destDataSize)
        {
            // Handle output assembly data - copy from source (g_assembly_data096) to destination (g_assembly_data064)
            // Use the smaller of the two sizes to prevent buffer overflow
            nuint copySize = sourceDataSize < destDataSize ? sourceDataSize : destDataSize;

            for (nuint i = 0; i < copySize; i++)
            {
                destData[i] = sourceData[i];
            }

            Console.WriteLine($"Output assembly data handled: {copySize} bytes copied from g_assembly_data096 ({sourceDataSize} bytes) to g_assembly_data064 ({destDataSize} bytes)");
        }

        [UnmanagedCallersOnly(EntryPoint = "handleAppInputAssemblyData")]
        public static unsafe void HandleAppInputAssemblyData(byte* inputData, nuint inputDataSize, byte* outputData, nuint outputDataSize)
        {
            Console.WriteLine("=== Handling Input Assembly Data ===");

            // Display and modify g_assembly_data064 (input assembly) contents
            Console.Write($"g_assembly_data064 ({inputDataSize} bytes): ");
            for (nuint i = 0; i < inputDataSize; i++)
            {
                Console.Write($"{++inputData[i]:X2} ");
            }
            Console.WriteLine();

            // Display g_assembly_data096 (output assembly) contents
            Console.Write($"g_assembly_data096 ({outputDataSize} bytes): ");
            for (nuint i = 0; i < outputDataSize; i++)
            {
                Console.Write($"{outputData[i]:X2} ");
            }
            Console.WriteLine();

            Console.WriteLine("Input assembly data processing completed.");
            Console.WriteLine("=====================================");
        }
    }
}
