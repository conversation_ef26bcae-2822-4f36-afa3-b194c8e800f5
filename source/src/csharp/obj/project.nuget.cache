{"version": 2, "dgSpecHash": "Pn5R0XfDSjg=", "success": true, "projectFilePath": "/root/FXOpENer/source/src/csharp/FXEIPDelegation.csproj", "expectedPackageFiles": ["/root/.nuget/packages/microsoft.dotnet.ilcompiler/8.0.18/microsoft.dotnet.ilcompiler.8.0.18.nupkg.sha512", "/root/.nuget/packages/microsoft.net.illink.tasks/8.0.18/microsoft.net.illink.tasks.8.0.18.nupkg.sha512", "/root/.nuget/packages/runtime.linux-arm64.microsoft.dotnet.ilcompiler/8.0.18/runtime.linux-arm64.microsoft.dotnet.ilcompiler.8.0.18.nupkg.sha512", "/root/.nuget/packages/microsoft.netcore.app.runtime.linux-arm64/8.0.18/microsoft.netcore.app.runtime.linux-arm64.8.0.18.nupkg.sha512", "/root/.nuget/packages/microsoft.aspnetcore.app.runtime.linux-arm64/8.0.18/microsoft.aspnetcore.app.runtime.linux-arm64.8.0.18.nupkg.sha512", "/root/.nuget/packages/runtime.linux-x64.microsoft.dotnet.ilcompiler/8.0.18/runtime.linux-x64.microsoft.dotnet.ilcompiler.8.0.18.nupkg.sha512"], "logs": []}