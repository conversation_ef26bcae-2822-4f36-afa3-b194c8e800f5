{"version": 3, "targets": {"net8.0": {"Microsoft.DotNet.ILCompiler/8.0.18": {"type": "package", "build": {"build/Microsoft.DotNet.ILCompiler.props": {}}}, "Microsoft.NET.ILLink.Tasks/8.0.18": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}}, "net8.0/linux-arm64": {"Microsoft.DotNet.ILCompiler/8.0.18": {"type": "package", "dependencies": {"runtime.linux-arm64.Microsoft.DotNet.ILCompiler": "8.0.18"}, "build": {"build/Microsoft.DotNet.ILCompiler.props": {}}}, "Microsoft.NET.ILLink.Tasks/8.0.18": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "runtime.linux-arm64.Microsoft.DotNet.ILCompiler/8.0.18": {"type": "package"}}}, "libraries": {"Microsoft.DotNet.ILCompiler/8.0.18": {"sha512": "tqfcjG/zm193qDP5FN0vdvqo0KpBzSpnu6M5IpZdn3sU5y0OS/dUrqqYrqlc6uaSlZEuHA5/wQNX4nd6ILx+Ew==", "type": "package", "path": "microsoft.dotnet.ilcompiler/8.0.18", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/BuildFrameworkNativeObjects.proj", "build/Microsoft.DotNet.ILCompiler.SingleEntry.targets", "build/Microsoft.DotNet.ILCompiler.props", "build/Microsoft.NETCore.Native.Publish.targets", "build/Microsoft.NETCore.Native.Unix.targets", "build/Microsoft.NETCore.Native.Windows.targets", "build/Microsoft.NETCore.Native.targets", "build/NativeAOT.natstepfilter", "build/NativeAOT.natvis", "build/WindowsAPIs.txt", "build/findvcvarsall.bat", "microsoft.dotnet.ilcompiler.8.0.18.nupkg.sha512", "microsoft.dotnet.ilcompiler.nuspec", "runtime.json", "tools/netstandard/ILCompiler.Build.Tasks.deps.json", "tools/netstandard/ILCompiler.Build.Tasks.dll", "tools/netstandard/ILCompiler.Build.Tasks.pdb"]}, "Microsoft.NET.ILLink.Tasks/8.0.18": {"sha512": "OiXqr2YIBEV9dsAWEtasK470ALyJ0VxJ9k4MotOxlWV6HeEgrJKYMW4HHj1OCCXvqE0/A25wEKPkpfiBARgDZA==", "type": "package", "path": "microsoft.net.illink.tasks/8.0.18", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "Sdk/Sdk.props", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/ILLink.CodeFixProvider.dll", "analyzers/dotnet/cs/ILLink.RoslynAnalyzer.dll", "build/Microsoft.NET.ILLink.Analyzers.props", "build/Microsoft.NET.ILLink.Tasks.props", "build/Microsoft.NET.ILLink.targets", "microsoft.net.illink.tasks.8.0.18.nupkg.sha512", "microsoft.net.illink.tasks.nuspec", "tools/net472/ILLink.Tasks.dll", "tools/net472/ILLink.Tasks.dll.config", "tools/net472/Mono.Cecil.Mdb.dll", "tools/net472/Mono.Cecil.Pdb.dll", "tools/net472/Mono.Cecil.Rocks.dll", "tools/net472/Mono.Cecil.dll", "tools/net472/Sdk/Sdk.props", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net472/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net472/build/Microsoft.NET.ILLink.Tasks.props", "tools/net472/build/Microsoft.NET.ILLink.targets", "tools/net8.0/ILLink.Tasks.deps.json", "tools/net8.0/ILLink.Tasks.dll", "tools/net8.0/Mono.Cecil.Mdb.dll", "tools/net8.0/Mono.Cecil.Pdb.dll", "tools/net8.0/Mono.Cecil.Rocks.dll", "tools/net8.0/Mono.Cecil.dll", "tools/net8.0/Sdk/Sdk.props", "tools/net8.0/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net8.0/build/Microsoft.NET.ILLink.Tasks.props", "tools/net8.0/build/Microsoft.NET.ILLink.targets", "tools/net8.0/illink.deps.json", "tools/net8.0/illink.dll", "tools/net8.0/illink.runtimeconfig.json", "useSharedDesignerContext.txt"]}, "runtime.linux-arm64.Microsoft.DotNet.ILCompiler/8.0.18": {"sha512": "Kjt1lmAmm6q5le+eCGwZnzlrcS1CTUy2KSDIzUyt7Iv0zoYDP454IBLedJ9F/Gb76xJHbqAcv0eePEggD9BRrw==", "type": "package", "path": "runtime.linux-arm64.microsoft.dotnet.ilcompiler/8.0.18", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/BuildFrameworkNativeObjects.proj", "build/Microsoft.DotNet.ILCompiler.SingleEntry.targets", "build/Microsoft.DotNet.ILCompiler.props", "build/Microsoft.NETCore.Native.Publish.targets", "build/Microsoft.NETCore.Native.Unix.targets", "build/Microsoft.NETCore.Native.Windows.targets", "build/Microsoft.NETCore.Native.targets", "build/NativeAOT.natstepfilter", "build/NativeAOT.natvis", "build/WindowsAPIs.txt", "build/findvcvarsall.bat", "framework/Microsoft.CSharp.dll", "framework/Microsoft.CSharp.pdb", "framework/Microsoft.VisualBasic.Core.dll", "framework/Microsoft.VisualBasic.Core.pdb", "framework/Microsoft.VisualBasic.dll", "framework/Microsoft.VisualBasic.pdb", "framework/Microsoft.Win32.Primitives.dll", "framework/Microsoft.Win32.Primitives.pdb", "framework/Microsoft.Win32.Registry.dll", "framework/Microsoft.Win32.Registry.pdb", "framework/System.AppContext.dll", "framework/System.AppContext.pdb", "framework/System.Buffers.dll", "framework/System.Buffers.pdb", "framework/System.Collections.Concurrent.dll", "framework/System.Collections.Concurrent.pdb", "framework/System.Collections.Immutable.dll", "framework/System.Collections.Immutable.pdb", "framework/System.Collections.NonGeneric.dll", "framework/System.Collections.NonGeneric.pdb", "framework/System.Collections.Specialized.dll", "framework/System.Collections.Specialized.pdb", "framework/System.Collections.dll", "framework/System.Collections.pdb", "framework/System.ComponentModel.Annotations.dll", "framework/System.ComponentModel.Annotations.pdb", "framework/System.ComponentModel.DataAnnotations.dll", "framework/System.ComponentModel.DataAnnotations.pdb", "framework/System.ComponentModel.EventBasedAsync.dll", "framework/System.ComponentModel.EventBasedAsync.pdb", "framework/System.ComponentModel.Primitives.dll", "framework/System.ComponentModel.Primitives.pdb", "framework/System.ComponentModel.TypeConverter.dll", "framework/System.ComponentModel.TypeConverter.pdb", "framework/System.ComponentModel.dll", "framework/System.ComponentModel.pdb", "framework/System.Configuration.dll", "framework/System.Configuration.pdb", "framework/System.Console.dll", "framework/System.Console.pdb", "framework/System.Core.dll", "framework/System.Core.pdb", "framework/System.Data.Common.dll", "framework/System.Data.Common.pdb", "framework/System.Data.DataSetExtensions.dll", "framework/System.Data.DataSetExtensions.pdb", "framework/System.Data.dll", "framework/System.Data.pdb", "framework/System.Diagnostics.Contracts.dll", "framework/System.Diagnostics.Contracts.pdb", "framework/System.Diagnostics.Debug.dll", "framework/System.Diagnostics.Debug.pdb", "framework/System.Diagnostics.DiagnosticSource.dll", "framework/System.Diagnostics.DiagnosticSource.pdb", "framework/System.Diagnostics.FileVersionInfo.dll", "framework/System.Diagnostics.FileVersionInfo.pdb", "framework/System.Diagnostics.Process.dll", "framework/System.Diagnostics.Process.pdb", "framework/System.Diagnostics.StackTrace.dll", "framework/System.Diagnostics.StackTrace.pdb", "framework/System.Diagnostics.TextWriterTraceListener.dll", "framework/System.Diagnostics.TextWriterTraceListener.pdb", "framework/System.Diagnostics.Tools.dll", "framework/System.Diagnostics.Tools.pdb", "framework/System.Diagnostics.TraceSource.dll", "framework/System.Diagnostics.TraceSource.pdb", "framework/System.Diagnostics.Tracing.dll", "framework/System.Diagnostics.Tracing.pdb", "framework/System.Drawing.Primitives.dll", "framework/System.Drawing.Primitives.pdb", "framework/System.Drawing.dll", "framework/System.Drawing.pdb", "framework/System.Dynamic.Runtime.dll", "framework/System.Dynamic.Runtime.pdb", "framework/System.Formats.Asn1.dll", "framework/System.Formats.Asn1.pdb", "framework/System.Formats.Tar.dll", "framework/System.Formats.Tar.pdb", "framework/System.Globalization.Calendars.dll", "framework/System.Globalization.Calendars.pdb", "framework/System.Globalization.Extensions.dll", "framework/System.Globalization.Extensions.pdb", "framework/System.Globalization.dll", "framework/System.Globalization.pdb", "framework/System.IO.Compression.Brotli.dll", "framework/System.IO.Compression.Brotli.pdb", "framework/System.IO.Compression.FileSystem.dll", "framework/System.IO.Compression.FileSystem.pdb", "framework/System.IO.Compression.ZipFile.dll", "framework/System.IO.Compression.ZipFile.pdb", "framework/System.IO.Compression.dll", "framework/System.IO.Compression.pdb", "framework/System.IO.FileSystem.AccessControl.dll", "framework/System.IO.FileSystem.AccessControl.pdb", "framework/System.IO.FileSystem.DriveInfo.dll", "framework/System.IO.FileSystem.DriveInfo.pdb", "framework/System.IO.FileSystem.Primitives.dll", "framework/System.IO.FileSystem.Primitives.pdb", "framework/System.IO.FileSystem.Watcher.dll", "framework/System.IO.FileSystem.Watcher.pdb", "framework/System.IO.FileSystem.dll", "framework/System.IO.FileSystem.pdb", "framework/System.IO.IsolatedStorage.dll", "framework/System.IO.IsolatedStorage.pdb", "framework/System.IO.MemoryMappedFiles.dll", "framework/System.IO.MemoryMappedFiles.pdb", "framework/System.IO.Pipes.AccessControl.dll", "framework/System.IO.Pipes.AccessControl.pdb", "framework/System.IO.Pipes.dll", "framework/System.IO.Pipes.pdb", "framework/System.IO.UnmanagedMemoryStream.dll", "framework/System.IO.UnmanagedMemoryStream.pdb", "framework/System.IO.dll", "framework/System.IO.pdb", "framework/System.Linq.Expressions.dll", "framework/System.Linq.Expressions.pdb", "framework/System.Linq.Parallel.dll", "framework/System.Linq.Parallel.pdb", "framework/System.Linq.Queryable.dll", "framework/System.Linq.Queryable.pdb", "framework/System.Linq.dll", "framework/System.Linq.pdb", "framework/System.Memory.dll", "framework/System.Memory.pdb", "framework/System.Net.Http.Json.dll", "framework/System.Net.Http.Json.pdb", "framework/System.Net.Http.dll", "framework/System.Net.Http.pdb", "framework/System.Net.HttpListener.dll", "framework/System.Net.HttpListener.pdb", "framework/System.Net.Mail.dll", "framework/System.Net.Mail.pdb", "framework/System.Net.NameResolution.dll", "framework/System.Net.NameResolution.pdb", "framework/System.Net.NetworkInformation.dll", "framework/System.Net.NetworkInformation.pdb", "framework/System.Net.Ping.dll", "framework/System.Net.Ping.pdb", "framework/System.Net.Primitives.dll", "framework/System.Net.Primitives.pdb", "framework/System.Net.Quic.dll", "framework/System.Net.Quic.pdb", "framework/System.Net.Requests.dll", "framework/System.Net.Requests.pdb", "framework/System.Net.Security.dll", "framework/System.Net.Security.pdb", "framework/System.Net.ServicePoint.dll", "framework/System.Net.ServicePoint.pdb", "framework/System.Net.Sockets.dll", "framework/System.Net.Sockets.pdb", "framework/System.Net.WebClient.dll", "framework/System.Net.WebClient.pdb", "framework/System.Net.WebHeaderCollection.dll", "framework/System.Net.WebHeaderCollection.pdb", "framework/System.Net.WebProxy.dll", "framework/System.Net.WebProxy.pdb", "framework/System.Net.WebSockets.Client.dll", "framework/System.Net.WebSockets.Client.pdb", "framework/System.Net.WebSockets.dll", "framework/System.Net.WebSockets.pdb", "framework/System.Net.dll", "framework/System.Net.pdb", "framework/System.Numerics.Vectors.dll", "framework/System.Numerics.Vectors.pdb", "framework/System.Numerics.dll", "framework/System.Numerics.pdb", "framework/System.ObjectModel.dll", "framework/System.ObjectModel.pdb", "framework/System.Private.DataContractSerialization.dll", "framework/System.Private.DataContractSerialization.pdb", "framework/System.Private.Uri.dll", "framework/System.Private.Uri.pdb", "framework/System.Private.Xml.Linq.dll", "framework/System.Private.Xml.Linq.pdb", "framework/System.Private.Xml.dll", "framework/System.Private.Xml.pdb", "framework/System.Reflection.DispatchProxy.dll", "framework/System.Reflection.DispatchProxy.pdb", "framework/System.Reflection.Emit.ILGeneration.dll", "framework/System.Reflection.Emit.ILGeneration.pdb", "framework/System.Reflection.Emit.Lightweight.dll", "framework/System.Reflection.Emit.Lightweight.pdb", "framework/System.Reflection.Emit.dll", "framework/System.Reflection.Emit.pdb", "framework/System.Reflection.Extensions.dll", "framework/System.Reflection.Extensions.pdb", "framework/System.Reflection.Metadata.dll", "framework/System.Reflection.Metadata.pdb", "framework/System.Reflection.Primitives.dll", "framework/System.Reflection.Primitives.pdb", "framework/System.Reflection.TypeExtensions.dll", "framework/System.Reflection.TypeExtensions.pdb", "framework/System.Reflection.dll", "framework/System.Reflection.pdb", "framework/System.Resources.Reader.dll", "framework/System.Resources.Reader.pdb", "framework/System.Resources.ResourceManager.dll", "framework/System.Resources.ResourceManager.pdb", "framework/System.Resources.Writer.dll", "framework/System.Resources.Writer.pdb", "framework/System.Runtime.CompilerServices.Unsafe.dll", "framework/System.Runtime.CompilerServices.Unsafe.pdb", "framework/System.Runtime.CompilerServices.VisualC.dll", "framework/System.Runtime.CompilerServices.VisualC.pdb", "framework/System.Runtime.Extensions.dll", "framework/System.Runtime.Extensions.pdb", "framework/System.Runtime.Handles.dll", "framework/System.Runtime.Handles.pdb", "framework/System.Runtime.InteropServices.JavaScript.dll", "framework/System.Runtime.InteropServices.JavaScript.pdb", "framework/System.Runtime.InteropServices.RuntimeInformation.dll", "framework/System.Runtime.InteropServices.RuntimeInformation.pdb", "framework/System.Runtime.InteropServices.dll", "framework/System.Runtime.InteropServices.pdb", "framework/System.Runtime.Intrinsics.dll", "framework/System.Runtime.Intrinsics.pdb", "framework/System.Runtime.Loader.dll", "framework/System.Runtime.Loader.pdb", "framework/System.Runtime.Numerics.dll", "framework/System.Runtime.Numerics.pdb", "framework/System.Runtime.Serialization.Formatters.dll", "framework/System.Runtime.Serialization.Formatters.pdb", "framework/System.Runtime.Serialization.Json.dll", "framework/System.Runtime.Serialization.Json.pdb", "framework/System.Runtime.Serialization.Primitives.dll", "framework/System.Runtime.Serialization.Primitives.pdb", "framework/System.Runtime.Serialization.Xml.dll", "framework/System.Runtime.Serialization.Xml.pdb", "framework/System.Runtime.Serialization.dll", "framework/System.Runtime.Serialization.pdb", "framework/System.Runtime.dll", "framework/System.Runtime.pdb", "framework/System.Security.AccessControl.dll", "framework/System.Security.AccessControl.pdb", "framework/System.Security.Claims.dll", "framework/System.Security.Claims.pdb", "framework/System.Security.Cryptography.Algorithms.dll", "framework/System.Security.Cryptography.Algorithms.pdb", "framework/System.Security.Cryptography.Cng.dll", "framework/System.Security.Cryptography.Cng.pdb", "framework/System.Security.Cryptography.Csp.dll", "framework/System.Security.Cryptography.Csp.pdb", "framework/System.Security.Cryptography.Encoding.dll", "framework/System.Security.Cryptography.Encoding.pdb", "framework/System.Security.Cryptography.OpenSsl.dll", "framework/System.Security.Cryptography.OpenSsl.pdb", "framework/System.Security.Cryptography.Primitives.dll", "framework/System.Security.Cryptography.Primitives.pdb", "framework/System.Security.Cryptography.X509Certificates.dll", "framework/System.Security.Cryptography.X509Certificates.pdb", "framework/System.Security.Cryptography.dll", "framework/System.Security.Cryptography.pdb", "framework/System.Security.Principal.Windows.dll", "framework/System.Security.Principal.Windows.pdb", "framework/System.Security.Principal.dll", "framework/System.Security.Principal.pdb", "framework/System.Security.SecureString.dll", "framework/System.Security.SecureString.pdb", "framework/System.Security.dll", "framework/System.Security.pdb", "framework/System.ServiceModel.Web.dll", "framework/System.ServiceModel.Web.pdb", "framework/System.ServiceProcess.dll", "framework/System.ServiceProcess.pdb", "framework/System.Text.Encoding.CodePages.dll", "framework/System.Text.Encoding.CodePages.pdb", "framework/System.Text.Encoding.Extensions.dll", "framework/System.Text.Encoding.Extensions.pdb", "framework/System.Text.Encoding.dll", "framework/System.Text.Encoding.pdb", "framework/System.Text.Encodings.Web.dll", "framework/System.Text.Encodings.Web.pdb", "framework/System.Text.Json.dll", "framework/System.Text.Json.pdb", "framework/System.Text.RegularExpressions.dll", "framework/System.Text.RegularExpressions.pdb", "framework/System.Threading.Channels.dll", "framework/System.Threading.Channels.pdb", "framework/System.Threading.Overlapped.dll", "framework/System.Threading.Overlapped.pdb", "framework/System.Threading.Tasks.Dataflow.dll", "framework/System.Threading.Tasks.Dataflow.pdb", "framework/System.Threading.Tasks.Extensions.dll", "framework/System.Threading.Tasks.Extensions.pdb", "framework/System.Threading.Tasks.Parallel.dll", "framework/System.Threading.Tasks.Parallel.pdb", "framework/System.Threading.Tasks.dll", "framework/System.Threading.Tasks.pdb", "framework/System.Threading.Thread.dll", "framework/System.Threading.Thread.pdb", "framework/System.Threading.ThreadPool.dll", "framework/System.Threading.ThreadPool.pdb", "framework/System.Threading.Timer.dll", "framework/System.Threading.Timer.pdb", "framework/System.Threading.dll", "framework/System.Threading.pdb", "framework/System.Transactions.Local.dll", "framework/System.Transactions.Local.pdb", "framework/System.Transactions.dll", "framework/System.Transactions.pdb", "framework/System.ValueTuple.dll", "framework/System.ValueTuple.pdb", "framework/System.Web.HttpUtility.dll", "framework/System.Web.HttpUtility.pdb", "framework/System.Web.dll", "framework/System.Web.pdb", "framework/System.Windows.dll", "framework/System.Windows.pdb", "framework/System.Xml.Linq.dll", "framework/System.Xml.Linq.pdb", "framework/System.Xml.ReaderWriter.dll", "framework/System.Xml.ReaderWriter.pdb", "framework/System.Xml.Serialization.dll", "framework/System.Xml.Serialization.pdb", "framework/System.Xml.XDocument.dll", "framework/System.Xml.XDocument.pdb", "framework/System.Xml.XPath.XDocument.dll", "framework/System.Xml.XPath.XDocument.pdb", "framework/System.Xml.XPath.dll", "framework/System.Xml.XPath.pdb", "framework/System.Xml.XmlDocument.dll", "framework/System.Xml.XmlDocument.pdb", "framework/System.Xml.XmlSerializer.dll", "framework/System.Xml.XmlSerializer.pdb", "framework/System.Xml.dll", "framework/System.Xml.pdb", "framework/System.dll", "framework/System.pdb", "framework/WindowsBase.dll", "framework/WindowsBase.pdb", "framework/libSystem.Globalization.Native.a", "framework/libSystem.Globalization.Native.so", "framework/libSystem.Globalization.Native.so.dbg", "framework/libSystem.IO.Compression.Native.a", "framework/libSystem.IO.Compression.Native.so", "framework/libSystem.IO.Compression.Native.so.dbg", "framework/libSystem.Native.a", "framework/libSystem.Native.so", "framework/libSystem.Native.so.dbg", "framework/libSystem.Net.Security.Native.a", "framework/libSystem.Net.Security.Native.so", "framework/libSystem.Net.Security.Native.so.dbg", "framework/libSystem.Security.Cryptography.Native.OpenSsl.a", "framework/libSystem.Security.Cryptography.Native.OpenSsl.so", "framework/libSystem.Security.Cryptography.Native.OpenSsl.so.dbg", "framework/mscorlib.dll", "framework/mscorlib.pdb", "framework/netstandard.dll", "framework/netstandard.pdb", "mibc/DotNet_Adhoc.mibc", "mibc/DotNet_FSharp.mibc", "mibc/DotNet_FirstTimeXP.mibc", "mibc/DotNet_HelloWorld.mibc", "mibc/DotNet_OrchardCore.mibc", "mibc/DotNet_TechEmpower.mibc", "native/src/libs/Common/delayloadhook_windows.cpp", "native/src/libs/Common/pal_atomic.h", "native/src/libs/Common/pal_compiler.h", "native/src/libs/Common/pal_config.h.in", "native/src/libs/Common/pal_error_common.h", "native/src/libs/Common/pal_io_common.h", "native/src/libs/Common/pal_networking_common.h", "native/src/libs/Common/pal_safecrt.h", "native/src/libs/Common/pal_ssl_types.h", "native/src/libs/Common/pal_types.h", "native/src/libs/Common/pal_utilities.h", "native/src/libs/Common/pal_x509_types.h", "native/src/libs/System.Globalization.Native/CMakeLists.txt", "native/src/libs/System.Globalization.Native/config.h.in", "native/src/libs/System.Globalization.Native/configure.cmake", "native/src/libs/System.Globalization.Native/entrypoints.c", "native/src/libs/System.Globalization.Native/pal_calendarData.c", "native/src/libs/System.Globalization.Native/pal_calendarData.h", "native/src/libs/System.Globalization.Native/pal_calendarData.m", "native/src/libs/System.Globalization.Native/pal_casing.c", "native/src/libs/System.Globalization.Native/pal_casing.h", "native/src/libs/System.Globalization.Native/pal_casing.m", "native/src/libs/System.Globalization.Native/pal_collation.c", "native/src/libs/System.Globalization.Native/pal_collation.h", "native/src/libs/System.Globalization.Native/pal_collation.m", "native/src/libs/System.Globalization.Native/pal_errors.h", "native/src/libs/System.Globalization.Native/pal_errors_internal.h", "native/src/libs/System.Globalization.Native/pal_icushim.c", "native/src/libs/System.Globalization.Native/pal_icushim.h", "native/src/libs/System.Globalization.Native/pal_icushim_internal.h", "native/src/libs/System.Globalization.Native/pal_icushim_internal_android.h", "native/src/libs/System.Globalization.Native/pal_icushim_static.c", "native/src/libs/System.Globalization.Native/pal_idna.c", "native/src/libs/System.Globalization.Native/pal_idna.h", "native/src/libs/System.Globalization.Native/pal_locale.c", "native/src/libs/System.Globalization.Native/pal_locale.h", "native/src/libs/System.Globalization.Native/pal_locale.m", "native/src/libs/System.Globalization.Native/pal_localeNumberData.c", "native/src/libs/System.Globalization.Native/pal_localeNumberData.h", "native/src/libs/System.Globalization.Native/pal_localeStringData.c", "native/src/libs/System.Globalization.Native/pal_localeStringData.h", "native/src/libs/System.Globalization.Native/pal_locale_internal.h", "native/src/libs/System.Globalization.Native/pal_normalization.c", "native/src/libs/System.Globalization.Native/pal_normalization.h", "native/src/libs/System.Globalization.Native/pal_timeZoneInfo.c", "native/src/libs/System.Globalization.Native/pal_timeZoneInfo.h", "native/src/libs/System.Security.Cryptography.Native/CMakeLists.txt", "native/src/libs/System.Security.Cryptography.Native/apibridge.c", "native/src/libs/System.Security.Cryptography.Native/apibridge.h", "native/src/libs/System.Security.Cryptography.Native/apibridge_30.c", "native/src/libs/System.Security.Cryptography.Native/apibridge_30.h", "native/src/libs/System.Security.Cryptography.Native/apibridge_30_rev.h", "native/src/libs/System.Security.Cryptography.Native/configure.cmake", "native/src/libs/System.Security.Cryptography.Native/entrypoints.c", "native/src/libs/System.Security.Cryptography.Native/extra_libs.cmake", "native/src/libs/System.Security.Cryptography.Native/openssl.c", "native/src/libs/System.Security.Cryptography.Native/openssl.h", "native/src/libs/System.Security.Cryptography.Native/openssl_1_0_structs.h", "native/src/libs/System.Security.Cryptography.Native/opensslshim.c", "native/src/libs/System.Security.Cryptography.Native/opensslshim.h", "native/src/libs/System.Security.Cryptography.Native/osslcompat_102.h", "native/src/libs/System.Security.Cryptography.Native/osslcompat_111.h", "native/src/libs/System.Security.Cryptography.Native/osslcompat_30.h", "native/src/libs/System.Security.Cryptography.Native/pal_asn1.c", "native/src/libs/System.Security.Cryptography.Native/pal_asn1.h", "native/src/libs/System.Security.Cryptography.Native/pal_bignum.c", "native/src/libs/System.Security.Cryptography.Native/pal_bignum.h", "native/src/libs/System.Security.Cryptography.Native/pal_bio.c", "native/src/libs/System.Security.Cryptography.Native/pal_bio.h", "native/src/libs/System.Security.Cryptography.Native/pal_crypto_config.h.in", "native/src/libs/System.Security.Cryptography.Native/pal_crypto_types.h", "native/src/libs/System.Security.Cryptography.Native/pal_dsa.c", "native/src/libs/System.Security.Cryptography.Native/pal_dsa.h", "native/src/libs/System.Security.Cryptography.Native/pal_ecc_import_export.c", "native/src/libs/System.Security.Cryptography.Native/pal_ecc_import_export.h", "native/src/libs/System.Security.Cryptography.Native/pal_ecdsa.c", "native/src/libs/System.Security.Cryptography.Native/pal_ecdsa.h", "native/src/libs/System.Security.Cryptography.Native/pal_eckey.c", "native/src/libs/System.Security.Cryptography.Native/pal_eckey.h", "native/src/libs/System.Security.Cryptography.Native/pal_err.c", "native/src/libs/System.Security.Cryptography.Native/pal_err.h", "native/src/libs/System.Security.Cryptography.Native/pal_evp.c", "native/src/libs/System.Security.Cryptography.Native/pal_evp.h", "native/src/libs/System.Security.Cryptography.Native/pal_evp_cipher.c", "native/src/libs/System.Security.Cryptography.Native/pal_evp_cipher.h", "native/src/libs/System.Security.Cryptography.Native/pal_evp_pkey.c", "native/src/libs/System.Security.Cryptography.Native/pal_evp_pkey.h", "native/src/libs/System.Security.Cryptography.Native/pal_evp_pkey_dsa.c", "native/src/libs/System.Security.Cryptography.Native/pal_evp_pkey_dsa.h", "native/src/libs/System.Security.Cryptography.Native/pal_evp_pkey_ecdh.c", "native/src/libs/System.Security.Cryptography.Native/pal_evp_pkey_ecdh.h", "native/src/libs/System.Security.Cryptography.Native/pal_evp_pkey_eckey.c", "native/src/libs/System.Security.Cryptography.Native/pal_evp_pkey_eckey.h", "native/src/libs/System.Security.Cryptography.Native/pal_evp_pkey_rsa.c", "native/src/libs/System.Security.Cryptography.Native/pal_evp_pkey_rsa.h", "native/src/libs/System.Security.Cryptography.Native/pal_hmac.c", "native/src/libs/System.Security.Cryptography.Native/pal_hmac.h", "native/src/libs/System.Security.Cryptography.Native/pal_ocsp.c", "native/src/libs/System.Security.Cryptography.Native/pal_ocsp.h", "native/src/libs/System.Security.Cryptography.Native/pal_pkcs7.c", "native/src/libs/System.Security.Cryptography.Native/pal_pkcs7.h", "native/src/libs/System.Security.Cryptography.Native/pal_ssl.c", "native/src/libs/System.Security.Cryptography.Native/pal_ssl.h", "native/src/libs/System.Security.Cryptography.Native/pal_x509.c", "native/src/libs/System.Security.Cryptography.Native/pal_x509.h", "native/src/libs/System.Security.Cryptography.Native/pal_x509_name.c", "native/src/libs/System.Security.Cryptography.Native/pal_x509_name.h", "native/src/libs/System.Security.Cryptography.Native/pal_x509_root.c", "native/src/libs/System.Security.Cryptography.Native/pal_x509_root.h", "native/src/libs/System.Security.Cryptography.Native/pal_x509ext.c", "native/src/libs/System.Security.Cryptography.Native/pal_x509ext.h", "native/src/libs/build-local.sh", "native/src/minipal/asansupport.cpp", "native/src/minipal/configure.cmake", "native/src/minipal/cpufeatures.c", "native/src/minipal/cpufeatures.h", "native/src/minipal/cpuid.h", "native/src/minipal/entrypoints.h", "native/src/minipal/getexepath.h", "native/src/minipal/minipalconfig.h.in", "native/src/minipal/random.c", "native/src/minipal/random.h", "native/src/minipal/types.h", "native/src/minipal/utf8.c", "native/src/minipal/utf8.h", "native/src/minipal/utils.h", "runtime.linux-arm64.microsoft.dotnet.ilcompiler.8.0.18.nupkg.sha512", "runtime.linux-arm64.microsoft.dotnet.ilcompiler.nuspec", "sdk/System.Private.CoreLib.dll", "sdk/System.Private.CoreLib.pdb", "sdk/System.Private.CoreLib.xml", "sdk/System.Private.DisabledReflection.dll", "sdk/System.Private.DisabledReflection.pdb", "sdk/System.Private.DisabledReflection.xml", "sdk/System.Private.Reflection.Execution.dll", "sdk/System.Private.Reflection.Execution.pdb", "sdk/System.Private.Reflection.Execution.xml", "sdk/System.Private.StackTraceMetadata.dll", "sdk/System.Private.StackTraceMetadata.pdb", "sdk/System.Private.StackTraceMetadata.xml", "sdk/System.Private.TypeLoader.dll", "sdk/System.Private.TypeLoader.pdb", "sdk/System.Private.TypeLoader.xml", "sdk/libRuntime.ServerGC.a", "sdk/libRuntime.WorkstationGC.a", "sdk/libbootstrapper.o", "sdk/libbootstrapperdll.o", "sdk/libeventpipe-disabled.a", "sdk/libeventpipe-enabled.a", "sdk/libstdc++compat.a", "tools/ILCompiler.Compiler.pdb", "tools/ILCompiler.DependencyAnalysisFramework.pdb", "tools/ILCompiler.MetadataTransform.pdb", "tools/ILCompiler.RyuJit.pdb", "tools/ILCompiler.TypeSystem.pdb", "tools/ilc", "tools/ilc.dbg", "tools/libc++.so.1", "tools/libc++abi.so.1", "tools/libclrjit_universal_arm64_arm64.so", "tools/libclrjit_universal_arm_arm64.so", "tools/libclrjit_unix_x64_arm64.so", "tools/libclrjit_win_x64_arm64.so", "tools/libclrjit_win_x86_arm64.so", "tools/libjitinterface_arm64.so", "tools/libobjwriter.so", "tools/netstandard/ILCompiler.Build.Tasks.deps.json", "tools/netstandard/ILCompiler.Build.Tasks.dll", "tools/netstandard/ILCompiler.Build.Tasks.pdb"]}}, "projectFileDependencyGroups": {"net8.0": ["Microsoft.DotNet.ILCompiler >= 8.0.18", "Microsoft.NET.ILLink.Tasks >= 8.0.18"]}, "packageFolders": {"/root/.nuget/packages/": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "/root/FXOpENer/source/src/csharp/FXEIPDelegation.csproj", "projectName": "FXEIPDelegation", "projectPath": "/root/FXOpENer/source/src/csharp/FXEIPDelegation.csproj", "packagesPath": "/root/.nuget/packages/", "outputPath": "/root/FXOpENer/source/src/csharp/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/root/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.DotNet.ILCompiler": {"suppressParent": "All", "target": "Package", "version": "[8.0.18, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.18, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-arm64", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm64", "version": "[8.0.18, 8.0.18]"}, {"name": "runtime.linux-x64.Microsoft.DotNet.ILCompiler", "version": "[8.0.18, 8.0.18]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/8.0.412/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-arm64": {"#import": []}}}}