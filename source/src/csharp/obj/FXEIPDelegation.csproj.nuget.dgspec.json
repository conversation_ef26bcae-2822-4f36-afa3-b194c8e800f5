{"format": 1, "restore": {"/root/FXOpENer/source/src/csharp/FXEIPDelegation.csproj": {}}, "projects": {"/root/FXOpENer/source/src/csharp/FXEIPDelegation.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/root/FXOpENer/source/src/csharp/FXEIPDelegation.csproj", "projectName": "FXEIPDelegation", "projectPath": "/root/FXOpENer/source/src/csharp/FXEIPDelegation.csproj", "packagesPath": "/root/.nuget/packages/", "outputPath": "/root/FXOpENer/source/src/csharp/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/root/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.DotNet.ILCompiler": {"suppressParent": "All", "target": "Package", "version": "[8.0.18, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.18, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-arm64", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-arm64", "version": "[8.0.18, 8.0.18]"}, {"name": "runtime.linux-x64.Microsoft.DotNet.ILCompiler", "version": "[8.0.18, 8.0.18]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/8.0.412/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-arm64": {"#import": []}}}}}