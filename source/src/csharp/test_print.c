#include <stdio.h>
#include <dlfcn.h>

int main() {
    void* handle = dlopen("/root/FXOpENer/bin/posix/libOpenerPrintHelper.so", RTLD_LAZY);
    if (!handle) {
        printf("Cannot load library: %s\n", dlerror());
        return 1;
    }
    
    void (*print_hello)(void) = dlsym(handle, "PrintHelloWorld");
    if (!print_hello) {
        printf("Cannot find function: %s\n", dlerror());
        dlclose(handle);
        return 1;
    }
    
    printf("Calling C# PrintHelloWorld function:\n");
    print_hello();
    
    sleep(1);
    dlclose(handle);
    return 0;
}
