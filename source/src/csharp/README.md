# FXEIPDelegation - OpENer C# 互操作库

这个模块提供了C#与C代码的互操作功能，可以被OpENer EtherNet/IP堆栈调用。使用.NET NativeAOT技术生成原生共享库，支持x64和ARM64架构。

## 功能

- `PrintHelloWorld()` - 打印 "Hello world"，类似于 `printf("Hello world\n")`
- 支持多架构：x64 (linux-x64) 和 ARM64 (linux-arm64)
- 自动架构检测和库加载
- 原生共享库，无需.NET运行时

## 文件说明

- `FXEIPDelegation.cs` - C#实现的打印函数，使用UnmanagedCallersOnly属性
- `FXEIPDelegation.csproj` - C#项目文件，配置NativeAOT编译
- `gcc-wrapper.sh` - GCC包装脚本，用于ARM64交叉编译
- `../ports/POSIX/sample_application/csharp_interop.h` - C头文件，定义C#互操作接口
- `../ports/POSIX/sample_application/csharp_interop.c` - C包装器，动态加载原生.so库

## 构建方法

### 构建x64版本
```bash
dotnet publish FXEIPDelegation.csproj -c Release -r linux-x64 --self-contained
```

### 构建ARM64版本
```bash
# 需要先安装ARM64交叉编译工具链
apt-get install -y gcc-aarch64-linux-gnu g++-aarch64-linux-gnu libc6-dev-arm64-cross zlib1g-dev:arm64

# 编译ARM64版本
dotnet publish FXEIPDelegation.csproj -c Release -r linux-arm64 --self-contained
```

### 自动构建脚本
```bash
chmod +x build.sh
./build.sh
```

## 生成的文件

编译成功后会生成以下文件：

### x64版本
- `bin/Release/net8.0/linux-x64/native/FXEIPDelegation.so` - x64原生共享库
- `bin/Release/net8.0/linux-x64/publish/FXEIPDelegation.so` - 发布版本

### ARM64版本
- `bin/Release/net8.0/linux-arm64/native/FXEIPDelegation.so` - ARM64原生共享库
- `bin/Release/net8.0/linux-arm64/publish/FXEIPDelegation.so` - 发布版本

## 使用方法

### 初始化
在程序启动时调用初始化函数：
```c
// 在main函数或适当位置调用
InitCsharpInterop();
```

### 调用函数
```c
void some_function() {
    PrintHelloWorld();  // 打印 "Hello world"
}
```

## 在OpENer中的集成

已经在以下位置集成：

1. **初始化**: `source/src/ports/POSIX/sample_application/sampleapplication.c` 的main函数中调用 `InitCsharpInterop()`
2. **使用**: `AfterAssemblyDataReceived` 函数中调用 `PrintHelloWorld()`

## 架构支持和自动检测

C包装器会自动检测运行时架构并加载相应的库：

- **ARM64系统**: 优先加载 `libFXEIPDelegation_arm64.so`
- **x64系统**: 加载 `libFXEIPDelegation.so`
- **备用方案**: 如果架构特定库不存在，会尝试加载通用库
- **最终备用**: 如果所有库都无法加载，使用C的printf

## 技术特性

- **NativeAOT编译**: 生成原生机器码，无需.NET运行时
- **UnmanagedCallersOnly**: C#函数可直接被C代码调用
- **交叉编译**: 支持在x64主机上编译ARM64目标
- **动态加载**: 使用dlopen在运行时加载库
- **错误处理**: 完善的错误处理和备用机制

## 故障排除

### 常见问题

1. **objcopy错误**: 已通过禁用调试符号提取解决
2. **交叉编译失败**: 确保安装了完整的ARM64工具链
3. **库加载失败**: 检查库文件路径和权限
4. **段错误**: 避免调用dlclose()，让系统自动清理

### 调试信息

C包装器包含详细的调试输出，显示：
- 检测到的系统架构
- 尝试加载的库路径
- 加载成功/失败状态

## 依赖要求

### 编译时
- .NET 8.0 SDK
- GCC (本地编译)
- aarch64-linux-gnu-gcc (ARM64交叉编译)
- 相应架构的系统库

### 运行时
- 无需.NET运行时（NativeAOT）
- 标准C库 (libc)
- 数学库 (libm)
