/*******************************************************************************
 * Copyright (c) 2025, OpENer Project
 * All rights reserved.
 *
 ******************************************************************************/

#include "csharp_interop.h"
#include <stdio.h>
#include <dlfcn.h>
#include <stdlib.h>
#include <string.h>

/* Function pointer for C# function */
static void (*cs_print_hello_world)() = NULL;
static void (*cs_copy_assembly_data)(void*, void*, int) = NULL;
static void* dll_handle = NULL;

/* Initialize the C# DLL */
int InitCsharpInterop() {
    if (dll_handle != NULL) {
        return 1; /* Already initialized */
    }

    /* Try to load the C# DLL from various possible locations */
    dll_handle = dlopen("./libFXEIPDelegation.so", RTLD_LAZY);
    if (!dll_handle) {
        dll_handle = dlopen("/root/FXOpENer/bin/posix/libFXEIPDelegation.so", RTLD_LAZY);
    }

    if (!dll_handle) {
        printf("ERROR: Cannot load C# DLL from any location\n");
        return 0; /* Cannot load DLL */
    }

    /* Get function pointers */
    cs_print_hello_world = dlsym(dll_handle, "PrintHelloWorld");
    cs_copy_assembly_data = dlsym(dll_handle, "CopyAssemblyData");

    if (!cs_print_hello_world || !cs_copy_assembly_data) {
        dlclose(dll_handle);
        dll_handle = NULL;
        return 0;
    }

    return 1;
}

/* Cleanup function */
static void cleanup_csharp_dll() {
    if (dll_handle) {
        dlclose(dll_handle);
        dll_handle = NULL;
        cs_print_hello_world = NULL;
        cs_copy_assembly_data = NULL;
    }
}

void PrintHelloWorld() {
    if (!dll_handle)
    {
        InitCsharpInterop();
    }

    if (cs_print_hello_world) {
        cs_print_hello_world();
    } else {
        printf("ERROR: C# function not found\n");
    }
}

void CopyAssemblyData(void* source_data, void* dest_data, int data_size) {
    if (!dll_handle)
    {
        InitCsharpInterop();
    }

    if (cs_copy_assembly_data) {
        cs_copy_assembly_data(source_data, dest_data, data_size);
    } else {
        printf("ERROR: C# CopyAssemblyData function not found, using fallback memcpy\n");
        /* Fallback to standard memcpy if C# function is not available */
        memcpy(dest_data, source_data, data_size);
    }
}

/* Register cleanup function to be called at exit */
__attribute__((constructor))
static void register_cleanup() {
    atexit(cleanup_csharp_dll);
}
