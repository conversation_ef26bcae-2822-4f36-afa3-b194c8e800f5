/*******************************************************************************
 * Copyright (c) 2025, OpENer Project
 * All rights reserved.
 *
 ******************************************************************************/

#include "csharp_interop.h"
#include <stdio.h>
#include <dlfcn.h>
#include <stdlib.h>
#include <string.h>

/* Function pointer for C# function */
static void (*cs_print_hello_world)() = NULL;
static void (*cs_handle_app_output_assembly_data)(void*, size_t, void*, size_t) = NULL;
static void (*cs_handle_app_input_assembly_data)(void*, size_t, void*, size_t) = NULL;
static void* dll_handle = NULL;

/* Initialize the C# DLL */
int InitCsharpInterop() {
    if (dll_handle != NULL) {
        return 1; /* Already initialized */
    }

    /* Try to load the C# DLL from various possible locations */
    dll_handle = dlopen("./libFXEIPDelegation.so", RTLD_LAZY);
    if (!dll_handle) {
        dll_handle = dlopen("/root/FXOpENer/bin/posix/libFXEIPDelegation.so", RTLD_LAZY);
    }

    if (!dll_handle) {
        printf("ERROR: Cannot load C# DLL from any location\n");
        return 0; /* Cannot load DLL */
    }

    /* Get function pointers */
    cs_print_hello_world = dlsym(dll_handle, "PrintHelloWorld");
    cs_handle_app_output_assembly_data = dlsym(dll_handle, "handleAppOutputAssemblyData");
    cs_handle_app_input_assembly_data = dlsym(dll_handle, "handleAppInputAssemblyData");

    if (!cs_print_hello_world || !cs_handle_app_output_assembly_data || !cs_handle_app_input_assembly_data) {
        dlclose(dll_handle);
        dll_handle = NULL;
        return 0;
    }

    return 1;
}

/* Cleanup function */
static void cleanup_csharp_dll() {
    if (dll_handle) {
        dlclose(dll_handle);
        dll_handle = NULL;
        cs_print_hello_world = NULL;
        cs_handle_app_output_assembly_data = NULL;
        cs_handle_app_input_assembly_data = NULL;
    }
}

void PrintHelloWorld() {
    if (!dll_handle)
    {
        InitCsharpInterop();
    }

    if (cs_print_hello_world) {
        cs_print_hello_world();
    } else {
        printf("ERROR: C# function not found\n");
    }
}

void HandleAppOutputAssemblyData(void* output_data, size_t output_data_size, void* input_data, size_t input_data_size) {
    if (!dll_handle)
    {
        InitCsharpInterop();
    }

    if (cs_handle_app_output_assembly_data) {
        cs_handle_app_output_assembly_data(output_data, output_data_size, input_data, input_data_size);
    } else {
        printf("ERROR: C# HandleAppOutputAssemblyData function not found, using fallback memcpy\n");
        /* Fallback to standard memcpy if C# function is not available */
        /* Use the smaller size to prevent buffer overflow */
        size_t copy_size = output_data_size < input_data_size ? output_data_size : input_data_size;
        memcpy(input_data, output_data, copy_size);
    }
}

void HandleAppInputAssemblyData(void* input_data, size_t input_data_size, void* output_data, size_t output_data_size) {
    if (!dll_handle)
    {
        InitCsharpInterop();
    }

    if (cs_handle_app_input_assembly_data) {
        cs_handle_app_input_assembly_data(input_data, input_data_size, output_data, output_data_size);
    } else {
        printf("ERROR: C# HandleAppInputAssemblyData function not found\n");
        /* Fallback: handle input assembly data using C printf */
        printf("=== Handling Input Assembly Data (C fallback) ===\n");

        /* Display and modify input data */
        printf("g_assembly_data064 (%zu bytes): ", input_data_size);
        for (size_t i = 0; i < input_data_size; i++) {
            printf("%02X ", ++((unsigned char*)input_data)[i]);
        }
        printf("\n");

        /* Display output data */
        printf("g_assembly_data096 (%zu bytes): ", output_data_size);
        for (size_t i = 0; i < output_data_size; i++) {
            printf("%02X ", ((unsigned char*)output_data)[i]);
        }
        printf("\n");
        printf("Input assembly data processing completed (C fallback).\n");
        printf("==================================================\n");
    }
}

/* Register cleanup function to be called at exit */
__attribute__((constructor))
static void register_cleanup() {
    atexit(cleanup_csharp_dll);
}
