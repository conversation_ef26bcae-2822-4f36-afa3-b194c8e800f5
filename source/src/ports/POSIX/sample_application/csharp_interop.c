/*******************************************************************************
 * Copyright (c) 2025, OpENer Project
 * All rights reserved.
 *
 ******************************************************************************/

#include "csharp_interop.h"
#include <stdio.h>
#include <dlfcn.h>
#include <stdlib.h>

/* Function pointer for C# function */
static void (*cs_print_hello_world)() = NULL;
static void* dll_handle = NULL;

/* Initialize the C# DLL */
int InitCsharpInterop() {
    if (dll_handle != NULL) {
        return 1; /* Already initialized */
    }

    /* Try to load the C# DLL from various possible locations */
    dll_handle = dlopen("./libFXEIPDelegation.so", RTLD_LAZY);
    if (!dll_handle) {
        dll_handle = dlopen("/root/FXOpENer/bin/posix/libFXEIPDelegation.so", RTLD_LAZY);
    }

    if (!dll_handle) {
        printf("ERROR: Cannot load C# DLL from any location\n");
        return 0; /* Cannot load DLL */
    }

    /* Get function pointer */
    cs_print_hello_world = dlsym(dll_handle, "PrintHelloWorld");

    if (!cs_print_hello_world) {
        dlclose(dll_handle);
        dll_handle = NULL;
        return 0;
    }

    return 1;
}

/* Cleanup function */
static void cleanup_csharp_dll() {
    if (dll_handle) {
        dlclose(dll_handle);
        dll_handle = NULL;
        cs_print_hello_world = NULL;
    }
}

void PrintHelloWorld() {
    if (!dll_handle)
    {
        InitCsharpInterop();
    }

    if (cs_print_hello_world) {
        cs_print_hello_world();
    } else {
        printf("ERROR: C# function not found\n");
    }
}

/* Register cleanup function to be called at exit */
__attribute__((constructor))
static void register_cleanup() {
    atexit(cleanup_csharp_dll);
}
