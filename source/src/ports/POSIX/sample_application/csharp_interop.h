/*******************************************************************************
 * Copyright (c) 2025, OpENer Project
 * All rights reserved.
 *
 ******************************************************************************/

#ifndef CSHARP_INTEROP_H_
#define CSHARP_INTEROP_H_

#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @file csharp_interop.h
 * @brief C# Interoperability interface for OpENer
 *
 * This header provides function declarations for calling C# functions
 * from C code. The actual implementation uses native .so libraries
 * generated by .NET NativeAOT compilation.
 */

/**
 * @brief Initialize C# interoperability
 *
 * This function initializes the C# native library loading.
 * Should be called once at program startup.
 *
 * @return 1 on success, 0 on failure
 */
extern int InitCsharpInterop(void);

/**
 * @brief Print "Hello world" message
 *
 * This function calls the C# implementation if available,
 * otherwise falls back to standard printf.
 */
extern void PrintHelloWorld(void);

/**
 * @brief Handle application output assembly data
 *
 * This function calls the C# implementation to handle output assembly data.
 * The C# code will process the data from the output assembly (g_assembly_data096)
 * and update the input assembly (g_assembly_data064) accordingly.
 *
 * @param source_data Pointer to source data array (g_assembly_data096)
 * @param source_data_size Size of source data array (g_assembly_data096) in bytes
 * @param dest_data Pointer to destination data array (g_assembly_data064)
 * @param dest_data_size Size of destination data array (g_assembly_data064) in bytes
 */
extern void HandleAppOutputAssemblyData(void* source_data, size_t source_data_size, void* dest_data, size_t dest_data_size);

/**
 * @brief Print assembly data contents
 *
 * This function calls the C# implementation to print the contents of both
 * assembly data arrays in hexadecimal format for debugging purposes.
 *
 * @param source_data Pointer to source data array (g_assembly_data096)
 * @param source_data_size Size of source data array (g_assembly_data096) in bytes
 * @param dest_data Pointer to destination data array (g_assembly_data064)
 * @param dest_data_size Size of destination data array (g_assembly_data064) in bytes
 */
extern void PrintAssemblyData(void* source_data, size_t source_data_size, void* dest_data, size_t dest_data_size);

#ifdef __cplusplus
}
#endif

#endif /* CSHARP_INTEROP_H_ */
