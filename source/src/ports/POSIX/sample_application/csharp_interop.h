/*******************************************************************************
 * Copyright (c) 2025, OpENer Project
 * All rights reserved.
 *
 ******************************************************************************/

#ifndef CSHARP_INTEROP_H_
#define CSHARP_INTEROP_H_

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @file csharp_interop.h
 * @brief C# Interoperability interface for OpENer
 *
 * This header provides function declarations for calling C# functions
 * from C code. The actual implementation uses native .so libraries
 * generated by .NET NativeAOT compilation.
 */

/**
 * @brief Initialize C# interoperability
 *
 * This function initializes the C# native library loading.
 * Should be called once at program startup.
 *
 * @return 1 on success, 0 on failure
 */
extern int InitCsharpInterop(void);

/**
 * @brief Print "Hello world" message
 *
 * This function calls the C# implementation if available,
 * otherwise falls back to standard printf.
 */
extern void PrintHelloWorld(void);

/**
 * @brief Copy assembly data from source to destination
 *
 * This function calls the C# implementation to copy data from
 * the output assembly (g_assembly_data096) to the input assembly (g_assembly_data064).
 *
 * @param source_data Pointer to source data array
 * @param dest_data Pointer to destination data array
 * @param data_size Size of data to copy in bytes
 */
extern void CopyAssemblyData(void* source_data, void* dest_data, int data_size);

#ifdef __cplusplus
}
#endif

#endif /* CSHARP_INTEROP_H_ */
