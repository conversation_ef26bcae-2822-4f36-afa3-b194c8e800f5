/*******************************************************************************
 * Copyright (c) 2025, OpENer Project
 * All rights reserved.
 *
 ******************************************************************************/

#ifndef CSHARP_INTEROP_H_
#define CSHARP_INTEROP_H_

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @file csharp_interop.h
 * @brief C# Interoperability interface for OpENer
 *
 * This header provides function declarations for calling C# functions
 * from C code. The actual implementation uses native .so libraries
 * generated by .NET NativeAOT compilation.
 */

/**
 * @brief Initialize C# interoperability
 *
 * This function initializes the C# native library loading.
 * Should be called once at program startup.
 *
 * @return 1 on success, 0 on failure
 */
extern int InitCsharpInterop(void);

/**
 * @brief Print "Hello world" message
 *
 * This function calls the C# implementation if available,
 * otherwise falls back to standard printf.
 */
extern void PrintHelloWorld(void);

#ifdef __cplusplus
}
#endif

#endif /* CSHARP_INTEROP_H_ */
