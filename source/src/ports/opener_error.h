/*******************************************************************************
 * Copyright (c) 2009, Rockwell Automation, Inc.
 * All rights reserved.
 *
 ******************************************************************************/

/** @file opener_error.h
 *  <AUTHOR>
 *  @brief This file includes the prototypes for error resolution functions like strerror_r or WSAGetLastError
 *
 */

/**
 * @brief Gets the error number or equivalent
 *
 * A delegate which implements how to get the error number from the system
 *
 * @return Error number
 */
int GetSocketErrorNumber(void);

/**
 * @brief Returns a human readable message for the given error number
 *
 * Returns a human readable error message to be used in logs and traces.
 * The error message shall not be a shared memory, like the classic strerror function, as such functions are non-reentrant
 * To free the space in which the error message is returned the user shall implement and use the function
 * FreeErrorMessage(char *)
 *
 * @return A human readable error message for the given error number
 */
char *GetErrorMessage(int error_number);


/**
 * @brief Frees the space of the error message generated by GetErrorMessage(int)
 *
 * This function shall implement an appropriate method to free the space allocated
 * by GetErrorMessage(int)
 */
void FreeErrorMessage(char *error_message);
